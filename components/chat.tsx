'use client';

import type { Attachment, UIMessage } from 'ai';
import { useChat } from '@ai-sdk/react';
import { useEffect, useRef, useState } from 'react';
import useSWR, { useSWRConfig } from 'swr';
import { ChatHeader } from '@/components/chat-header';
import type { Prompt, Vote } from '@/lib/db/schema';
import { fetcher, generateUUID } from '@/lib/utils';
import { Artifact } from './artifact';
import { MultimodalInput } from './multimodal-input';
import { Messages } from './messages';
import type { VisibilityType } from './visibility-selector';
import { useArtifactSelector } from '@/hooks/use-artifact';
import { unstable_serialize } from 'swr/infinite';
import { getChatHistoryPaginationKey } from './sidebar-history';
import { toast } from './toast';
import type { Session } from 'next-auth';
import { useSearchParams } from 'next/navigation';
import { useChatVisibility } from '@/hooks/use-chat-visibility';
import { useDefaultSelections } from '@/hooks/use-default-selections';
import { useAppState } from '@/hooks/use-app-state';
import { DEFAULT_CHAT_MODEL } from '@/lib/ai/models';
import { useCommentsState } from '@/hooks/use-comments';
import { CommentsSidebar } from './comments-sidebar';
import { useTTS } from '@/hooks/use-tts';
import type { TextPart } from 'ai';

export function Chat({
  id,
  initialMessages,
  initialChatModel,
  initialVisibilityType,
  isReadonly,
  session,
  autoResume,
  initialPromptId,
}: {
  id: string;
  initialMessages: Array<UIMessage>;
  initialChatModel: string;
  initialVisibilityType: VisibilityType;
  isReadonly: boolean;
  session: Session;
  autoResume: boolean;
  initialPromptId?: string;
}) {
  const { mutate } = useSWRConfig();
  // Get default selections based on user type
  const { defaultModelId, defaultPrompt, isAdmin, isLoading } =
    useDefaultSelections();

  // TTS functionality
  const {
    isAutoTTSEnabled,
    speak,
    hasUserSentMessage,
    markUserSentMessage,
    resetUserSentMessage,
  } = useTTS();

  // Use app state for selected model and prompt
  const {
    selectedChatModel,
    setSelectedChatModel,
    selectedPrompt,
    setSelectedPrompt,
  } = useAppState();

  // Initialize selected model from cookie or default based on user type
  useEffect(() => {
    // Always prioritize initialChatModel (from cookie) for admins on page load
    if (isAdmin && initialChatModel) {
      setSelectedChatModel(initialChatModel);
    }
    // For non-admins or when no model is selected, use the defaultModelId
    else if (!selectedChatModel) {
      setSelectedChatModel(isAdmin ? initialChatModel : defaultModelId || '');
    }
  }, [
    isAdmin,
    initialChatModel,
    defaultModelId,
    selectedChatModel,
    setSelectedChatModel,
  ]);

  // Create refs for the AI SDK to use
  const selectedChatModelRef = useRef<string | null>(selectedChatModel);
  const selectedPromptRef = useRef<Prompt | null>(selectedPrompt);

  const { visibilityType } = useChatVisibility({
    chatId: id,
    initialVisibilityType,
  });

  useEffect(() => {
    selectedPromptRef.current = selectedPrompt;
  }, [selectedPrompt]);

  useEffect(() => {
    selectedChatModelRef.current = selectedChatModel;
  }, [selectedChatModel]);

  const {
    messages,
    setMessages,
    handleSubmit,
    input,
    setInput,
    append,
    status,
    stop,
    reload,
    experimental_resume,
  } = useChat({
    id,
    initialMessages,
    experimental_throttle: 100,
    sendExtraMessageFields: true,
    generateId: generateUUID,
    experimental_prepareRequestBody: (body) => {
      const lastMessage = body.messages.at(-1);

      const requestBody = {
        id,
        message: lastMessage,
        selectedChatModel: selectedChatModelRef.current,
        selectedVisibilityType: visibilityType,
        promptId: selectedPromptRef.current?.id,
      };
      return requestBody;
    },

    onFinish: (message) => {
      mutate(unstable_serialize(getChatHistoryPaginationKey));

      // Auto-TTS: Speak the assistant's response if enabled and user has sent a message
      console.log('🎯 onFinish called:', {
        messageRole: message.role,
        isAutoTTSEnabled,
        hasUserSentMessage,
        messageId: message.id,
      });

      if (
        isAutoTTSEnabled &&
        message.role === 'assistant' &&
        hasUserSentMessage
      ) {
        const textFromParts = message.parts
          ?.filter((part): part is TextPart => part.type === 'text')
          .map((part) => part.text)
          .join('\n')
          .trim();

        console.log(
          '🔊 Auto-TTS triggering for message:',
          message.id,
          'Text length:',
          textFromParts?.length,
        );

        if (textFromParts) {
          speak(textFromParts, message.id);
        }
      } else {
        console.log('❌ Auto-TTS not triggered. Conditions:', {
          isAutoTTSEnabled,
          isAssistant: message.role === 'assistant',
          hasUserSentMessage,
        });
      }
    },
    onError: (error) => {
      toast({
        type: 'error',
        description: error.message,
      });
    },
  });

  // Set default prompt just once when it becomes available
  useEffect(() => {
    // Only set it once when defaultPrompt is available and selectedPrompt isn't
    if (defaultPrompt && !selectedPrompt && !isLoading) {
      setSelectedPrompt(defaultPrompt);
    }
  }, [defaultPrompt, selectedPrompt, isLoading]);

  useEffect(() => {
    if (autoResume) {
      experimental_resume();
    }

    // note: this hook has no dependencies since it only needs to run once
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Reset user sent message state when chat ID changes (navigating to different conversation)
  useEffect(() => {
    resetUserSentMessage();
  }, [id, resetUserSentMessage]);

  // Track when user sends a message by monitoring status changes
  useEffect(() => {
    if (status === 'submitted') {
      markUserSentMessage();
    }
  }, [status, markUserSentMessage]);

  const searchParams = useSearchParams();
  const query = searchParams.get('query');

  const [hasAppendedQuery, setHasAppendedQuery] = useState(false);

  useEffect(() => {
    if (query && !hasAppendedQuery) {
      append({
        role: 'user',
        content: query,
      });

      // Mark that user sent a message when using query parameter
      markUserSentMessage();

      setHasAppendedQuery(true);
      window.history.replaceState({}, '', `/chat/${id}`);
    }
  }, [query, append, hasAppendedQuery, id, markUserSentMessage]);

  const { data: votes } = useSWR<Array<Vote>>(
    messages.length >= 2 ? `/api/vote?chatId=${id}` : null,
    fetcher,
  );

  const [attachments, setAttachments] = useState<Array<Attachment>>([]);
  const isArtifactVisible = useArtifactSelector((state) => state.isVisible);

  // Comments functionality
  const {
    selectedMessageId,
    isCommentsSidebarOpen,
    openCommentsForMessage,
    closeCommentsSidebar,
    selectedText,
  } = useCommentsState();

  const handlePromptSelect = (prompt: Prompt) => {
    setSelectedPrompt(prompt);
  };

  return (
    <>
      <div className="flex flex-col min-w-0 h-dvh bg-background">
        <ChatHeader
          chatId={id}
          selectedModelId={selectedChatModel || DEFAULT_CHAT_MODEL}
          selectedVisibilityType={initialVisibilityType}
          isReadonly={isReadonly}
          session={session}
          onModelChange={setSelectedChatModel}
          onPromptSelect={handlePromptSelect}
          initialPromptId={initialPromptId}
        />

        <Messages
          chatId={id}
          status={status}
          votes={votes}
          messages={messages}
          setMessages={setMessages}
          reload={reload}
          isReadonly={isReadonly}
          isArtifactVisible={isArtifactVisible}
          onOpenComments={openCommentsForMessage}
        />

        <form className="flex mx-auto px-4 bg-background pb-4 md:pb-6 gap-2 w-full md:max-w-3xl">
          {!isReadonly && (
            <MultimodalInput
              chatId={id}
              input={input}
              setInput={setInput}
              handleSubmit={handleSubmit}
              status={status}
              stop={stop}
              attachments={attachments}
              setAttachments={setAttachments}
              messages={messages}
              setMessages={setMessages}
              append={append}
              selectedVisibilityType={visibilityType}
            />
          )}
        </form>
      </div>

      <Artifact
        chatId={id}
        input={input}
        setInput={setInput}
        handleSubmit={handleSubmit}
        status={status}
        stop={stop}
        attachments={attachments}
        setAttachments={setAttachments}
        append={append}
        messages={messages}
        setMessages={setMessages}
        reload={reload}
        votes={votes}
        isReadonly={isReadonly}
        selectedVisibilityType={visibilityType}
      />

      <CommentsSidebar
        messageId={selectedMessageId}
        isOpen={isCommentsSidebarOpen}
        onClose={closeCommentsSidebar}
        session={session}
        selectedText={selectedText}
      />
    </>
  );
}
