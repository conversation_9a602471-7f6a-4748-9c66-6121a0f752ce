'use client';

import {
  useState,
  useRef,
  useCallback,
  useEffect,
  createContext,
  useContext,
  type ReactNode,
} from 'react';
import { toast } from 'sonner';

export interface TTSState {
  isLoading: boolean;
  isPlaying: boolean;
  error: string | null;
  isAutoTTSEnabled: boolean;
  currentMessageId: string | null;
  hasUserSentMessage: boolean;
}

export interface TTSControls {
  speak: (text: string, messageId?: string) => Promise<void>;
  stop: () => void;
  toggleAutoTTS: () => void;
  setAutoTTS: (enabled: boolean) => void;
  isMessagePlaying: (messageId: string) => boolean;
  isMessageLoading: (messageId: string) => boolean;
  markUserSentMessage: () => void;
  resetUserSentMessage: () => void;
}

// Context for sharing TTS state across the app
const TTSContext = createContext<(TTSState & TTSControls) | null>(null);

export function TTSProvider({ children }: { children: ReactNode }) {
  const tts = useTTSInternal();
  return <TTSContext.Provider value={tts}>{children}</TTSContext.Provider>;
}

function useTTSInternal(): TTSState & TTSControls {
  const [isLoading, setIsLoading] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAutoTTSEnabled, setIsAutoTTSEnabled] = useState(false);
  const [currentMessageId, setCurrentMessageId] = useState<string | null>(null);
  const [hasUserSentMessage, setHasUserSentMessage] = useState(false);

  const audioRef = useRef<HTMLAudioElement | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Clean up audio when component unmounts
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const stop = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      audioRef.current = null;
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setIsPlaying(false);
    setIsLoading(false);
    setCurrentMessageId(null);
  }, []);

  const speak = useCallback(
    async (text: string, messageId?: string) => {
      console.log('🎤 speak() called:', { textLength: text.length, messageId });

      if (!text.trim()) {
        console.log('❌ speak() aborted: empty text');
        return;
      }

      // Stop any current playback
      stop();

      setIsLoading(true);
      setError(null);
      setCurrentMessageId(messageId || null);

      try {
        // Create abort controller for this request
        abortControllerRef.current = new AbortController();

        const response = await fetch('/api/tts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ text }),
          signal: abortControllerRef.current.signal,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to generate speech');
        }

        // Get audio data as blob
        const audioBlob = await response.blob();
        const audioUrl = URL.createObjectURL(audioBlob);

        // Create and configure audio element
        const audio = new Audio(audioUrl);
        audioRef.current = audio;

        // Set up event listeners
        audio.addEventListener('loadstart', () => {
          setIsLoading(false);
          setIsPlaying(true);
        });

        audio.addEventListener('ended', () => {
          setIsPlaying(false);
          setCurrentMessageId(null);
          URL.revokeObjectURL(audioUrl);
          audioRef.current = null;
        });

        audio.addEventListener('error', () => {
          setError('Failed to play audio');
          setIsPlaying(false);
          setIsLoading(false);
          setCurrentMessageId(null);
          URL.revokeObjectURL(audioUrl);
          audioRef.current = null;
        });

        // Start playback
        await audio.play();
      } catch (error: any) {
        if (error.name === 'AbortError') {
          // Request was aborted, don't show error
          return;
        }

        console.error('TTS error:', error);
        setError(error.message || 'Failed to generate speech');
        toast.error('Failed to generate speech');
      } finally {
        setIsLoading(false);
        abortControllerRef.current = null;
      }
    },
    [stop],
  );

  const toggleAutoTTS = useCallback(() => {
    setIsAutoTTSEnabled((prev) => {
      const newValue = !prev;
      console.log('🔄 Auto-TTS toggled:', prev, '→', newValue);
      return newValue;
    });
  }, []);

  const setAutoTTS = useCallback((enabled: boolean) => {
    setIsAutoTTSEnabled(enabled);
  }, []);

  const isMessagePlaying = useCallback(
    (messageId: string) => {
      return isPlaying && currentMessageId === messageId;
    },
    [isPlaying, currentMessageId],
  );

  const isMessageLoading = useCallback(
    (messageId: string) => {
      return isLoading && currentMessageId === messageId;
    },
    [isLoading, currentMessageId],
  );

  const markUserSentMessage = useCallback(() => {
    console.log(
      '🔄 User sent message - enabling auto-TTS for future responses',
    );
    setHasUserSentMessage(true);
  }, []);

  const resetUserSentMessage = useCallback(() => {
    console.log('🔄 Resetting user sent message state');
    setHasUserSentMessage(false);
  }, []);

  return {
    isLoading,
    isPlaying,
    error,
    isAutoTTSEnabled,
    currentMessageId,
    hasUserSentMessage,
    speak,
    stop,
    toggleAutoTTS,
    setAutoTTS,
    isMessagePlaying,
    isMessageLoading,
    markUserSentMessage,
    resetUserSentMessage,
  };
}

// Export the hook that uses context
export function useTTS(): TTSState & TTSControls {
  const context = useContext(TTSContext);
  if (!context) {
    throw new Error('useTTS must be used within a TTSProvider');
  }
  return context;
}
